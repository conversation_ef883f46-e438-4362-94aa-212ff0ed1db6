#!/usr/bin/env python3
"""
Image optimization script for WattWolf reference site
Converts PNG/JPEG images to WebP format and creates multiple sizes
"""

import os
from PIL import Image
import sys

def optimize_image(input_path, output_dir="a/optimized"):
    """Convert and optimize a single image"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    try:
        with Image.open(input_path) as img:
            # Convert to RGB if necessary (for WebP compatibility)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create a white background for transparent images
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Get base filename without extension
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            
            # Create different sizes
            sizes = [
                (400, 'small'),   # Mobile/thumbnail
                (800, 'medium'),  # Tablet
                (1200, 'large')   # Desktop
            ]
            
            original_width, original_height = img.size
            
            for max_width, size_name in sizes:
                if original_width <= max_width:
                    # If original is smaller, just use original size
                    resized = img.copy()
                else:
                    # Calculate proportional height
                    ratio = max_width / original_width
                    new_height = int(original_height * ratio)
                    resized = img.resize((max_width, new_height), Image.Resampling.LANCZOS)
                
                # Save as WebP
                webp_path = os.path.join(output_dir, f"{base_name}-{size_name}.webp")
                resized.save(webp_path, 'WebP', quality=85, optimize=True)
                print(f"Created: {webp_path}")
                
                # Also save original format as fallback
                fallback_path = os.path.join(output_dir, f"{base_name}-{size_name}.jpg")
                resized.save(fallback_path, 'JPEG', quality=85, optimize=True)
                print(f"Created: {fallback_path}")
            
            # Create full-size WebP version
            full_webp = os.path.join(output_dir, f"{base_name}.webp")
            img.save(full_webp, 'WebP', quality=90, optimize=True)
            print(f"Created: {full_webp}")
            
            return True
            
    except Exception as e:
        print(f"Error processing {input_path}: {e}")
        return False

def main():
    # Images currently used in the HTML
    current_images = [
        "a/ref-teal.jpeg",
        "a/ref-sheet-updated.png", 
        "a/baja.png",
        "a/Besties_Comm.png"
    ]
    
    print("Optimizing current images used in the website...")
    for img_path in current_images:
        if os.path.exists(img_path):
            print(f"\nProcessing: {img_path}")
            optimize_image(img_path)
        else:
            print(f"Warning: {img_path} not found")
    
    print("\nOptimization complete!")
    print("Check the 'a/optimized' directory for the optimized images.")

if __name__ == "__main__":
    main()
