<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>WattWolf's Ref 💙💚</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Character reference sheet for WattWolf - an agender wolf fursona with Baja Blast inspired colors. Features personality traits, art references, and commission examples for artists and friends." />
  <meta name="keywords" content="WattWolf, fursona, wolf, character reference, furry art, commission reference, Baja Blast colors" />

  <!-- Metadata for link previews -->
  <meta property="og:title" content="WattWolf's Ref 💙💚">
  <meta property="og:description" content="Art, reference, and personality info for Watt 💙💚">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://watt.goes-aw.ooo/ref-sheet">
  <meta property="og:image" content="https://watt.goes-aw.ooo/a/baja.png">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="1200">

  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<PERSON>Wolf's Ref 💙💚">
  <meta name="twitter:description" content="Art, reference, and personality info for Watt 💙💚">
  <meta name="twitter:image" content="https://watt.goes-aw.ooo/a/baja.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="/a/baja.png">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@600&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <style>
    :root {
      --baja-teal: #14e6c4;
      --electric-blue: #00c7ff;
      --neon-green: #0F0;
      --bg-dark: #0d1117;
      --text-main: #e9fafa;
      --text-muted: #c4e4e4;
      --card-bg: #1a1f25;
    }

    * {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 2rem 1rem;
      background: var(--bg-dark);
      color: var(--text-main);
      font-family: "Roboto", sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 100vh;
      animation: fadeIn 0.8s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }



  
   /* .title {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Orbitron", sans-serif;
  font-size: clamp(2rem, 5vw, 3rem);
  text-align: center;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  color: transparent;
  text-shadow:
    0 0 4px var(--electric-blue),
    0 0 8px #00d9bf,
    0 0 12px #00eb7f,
    0 0 16px var(--neon-green);
  margin: 0 0 1rem;
  line-height: 1.2;
}
*/

.title {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Orbitron", sans-serif;
  font-size: clamp(2rem, 5vw, 3rem);
  text-align: center;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin: 0 0 1rem;
  line-height: 1.2;
}

.title::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  filter: blur(10px);
  z-index: -1;
  margin-top: 5px;
}



.title-line {
  display: inline;
}

.emoji-row {
  display: none;
  gap: 0.5rem;
  font-size: 1.6em;
}

/*@media (min-width: 501px) {*/
  .title {
    flex-direction: row;
    gap: 0.5rem;
  }

  .emoji-row {
    display: none;
  }

  .title .emoji-inline {
    display: inline;
    font-size: 1.2em;
  }
/*}

@media (max-width: 500px) {
  .emoji-inline {
    display: none;
  }

  .emoji-row {
    display: flex;
    margin-top: 0.3rem;
  }
}*/
    
    .profile-link {
      font-family: "Roboto", sans-serif;
      margin-bottom: 2rem;
      font-size: 1rem;
      display: flex;
      justify-content: center;
    }

    .bluesky-link {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--baja-teal);
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      transition: all 0.3s ease;
      border: 1px solid transparent;
    }

    .bluesky-link:hover,
    .bluesky-link:focus {
      background: rgba(20, 230, 196, 0.1);
      border-color: var(--baja-teal);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(20, 230, 196, 0.2);
      outline: none;
    }

    .bluesky-icon {
      width: 1.2em;
      height: 1.2em;
      flex-shrink: 0;
    }

    .bio {
      max-width: 900px;
      background: #151a20;
      border: 2px solid var(--electric-blue);
      padding: 1.5rem;
      border-radius: 10px;
      margin-bottom: 2rem;
      position: relative;
      overflow: hidden;
      animation: slideInUp 0.6s ease-out 0.2s both;
    }

    @keyframes slideInUp {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }

    .bio h2 {
      margin-top: 0;
      font-family: "Orbitron", sans-serif;
      color: var(--electric-blue);
      font-size: 1.5rem;
    }

    .bio p {
      margin: 0.3rem 0;
    }

    .bio ul {
      padding-left: 1.2rem;
      margin: 0.5rem 0;
    }

    .personality-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.8rem;
      margin: 1rem 0;
    }

    .personality-trait {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.6rem 0.8rem;
      background: rgba(20, 230, 196, 0.1);
      border: 1px solid rgba(20, 230, 196, 0.3);
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: default;
    }

    .personality-trait:hover {
      background: rgba(20, 230, 196, 0.2);
      border-color: var(--baja-teal);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(20, 230, 196, 0.2);
    }

    .trait-icon {
      font-size: 1.2em;
      flex-shrink: 0;
    }

    .trait-text {
      font-size: 0.9rem;
      font-weight: 500;
    }

    @media (max-width: 600px) {
      .personality-grid {
        grid-template-columns: 1fr;
      }
    }

    /* Watt IRL Section */
    .watt-in-action {
      margin: 3rem 0;
      padding: 2rem;
      background: rgba(20, 230, 196, 0.05);
      border-radius: 12px;
      border: 1px solid rgba(20, 230, 196, 0.2);
    }

    .watt-in-action h2 {
      color: var(--electric-blue);
      font-family: "Orbitron", sans-serif;
      font-size: 1.8rem;
      margin-bottom: 0.5rem;
      text-align: center;
    }

    .section-description {
      text-align: center;
      color: var(--text-color);
      margin-bottom: 2rem;
      font-style: italic;
      opacity: 0.9;
    }

    .action-gallery {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-top: 1.5rem;
    }

    .action-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      border: 1px solid rgba(20, 230, 196, 0.2);
      position: relative;
    }

    .action-card:hover,
    .action-card:focus {
      transform: translateY(-5px) scale(1.02);
      box-shadow: 0 8px 25px rgba(20, 230, 196, 0.3);
      border-color: var(--baja-teal);
      outline: none;
    }

    .action-card picture,
    .action-card img {
      width: 100%;
      height: 200px;
      object-fit: cover;
      display: block;
    }

    .action-caption {
      padding: 1rem;
      text-align: left;
      font-weight: 400;
      color: var(--text-color);
      margin: 0;
      font-size: 0.85rem;
      background: rgba(20, 230, 196, 0.1);
      line-height: 1.4;
      min-height: 80px;
      display: flex;
      align-items: center;
    }

    .grid {
      display: grid;
      gap: 1.5rem;
      width: 100%;
      max-width: 1000px;
      grid-template-areas:
        "teal updated"
        "baja updated"
        "besties besties";
      grid-template-columns: 1fr 1fr;
    }

    .card {
      position: relative;
      background: var(--card-bg);
      border-radius: 10px;
      box-shadow: 0 0 0 3px rgba(20, 230, 196, 0.3);
      padding: 1rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      overflow: hidden;
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(20, 230, 196, 0.1), transparent);
      transition: left 0.5s;
      z-index: 1;
    }

    .card:hover::before {
      left: 100%;
    }

    .card:hover,
    .card:focus {
      transform: translateY(-8px) scale(1.02);
      box-shadow:
        0 0 20px var(--baja-teal),
        0 10px 30px rgba(20, 230, 196, 0.2);
      outline: 2px solid var(--electric-blue);
      outline-offset: 2px;
    }

    .card img {
      width: 100%;
      height: auto;
      border-radius: 6px;
      display: block;
      transition: opacity 0.3s ease;
    }

    .card img.loading {
      opacity: 0;
    }

    .card img.loaded {
      opacity: 1;
    }

    .image-placeholder {
      width: 100%;
      height: 200px;
      background: linear-gradient(90deg, #1a1f25 25%, #2a2f35 50%, #1a1f25 75%);
      background-size: 200% 100%;
      animation: shimmerLoad 1.5s infinite;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-muted);
      font-size: 0.9rem;
    }

    @keyframes shimmerLoad {
      0% { background-position: -200% 0; }
      100% { background-position: 200% 0; }
    }

    .caption {
      margin-top: 0.75rem;
      font-style: italic;
      font-weight: 500;
      color: var(--text-muted);
      text-align: center;
    }

    .caption a {
      color: var(--electric-blue);
      text-decoration: none;
    }

    .caption a:hover {
      text-decoration: underline;
    }

    .teal    { grid-area: teal; }
    .updated { grid-area: updated; }
    .baja    { grid-area: baja; }
    .besties { grid-area: besties; }

    /* Lightbox styles */
    .lightbox {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.9);
      animation: fadeIn 0.3s ease-out;
    }

    .lightbox.active {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .lightbox-content {
      position: relative;
      max-width: 90%;
      max-height: 90%;
      animation: zoomIn 0.3s ease-out;
    }

    .lightbox img {
      width: 100%;
      height: auto;
      border-radius: 8px;
      box-shadow: 0 0 30px rgba(20, 230, 196, 0.5);
    }

    .lightbox-close {
      position: absolute;
      top: -40px;
      right: 0;
      color: var(--text-main);
      font-size: 2rem;
      font-weight: bold;
      cursor: pointer;
      background: none;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      transition: all 0.2s;
    }

    .lightbox-close:hover,
    .lightbox-close:focus {
      color: var(--electric-blue);
      background: rgba(255, 255, 255, 0.1);
      outline: 2px solid var(--electric-blue);
    }

    @keyframes zoomIn {
      from { transform: scale(0.8); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }

    @media (max-width: 700px) {
      body {
        padding: 1rem 0.5rem;
      }

      .bio {
        padding: 1rem;
        margin-bottom: 1.5rem;
      }

      .grid {
        grid-template-areas:
          "teal"
          "updated"
          "baja"
          "besties";
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .card {
        padding: 0.8rem;
      }

      .lightbox-close {
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.7);
      }

      .lightbox-content {
        max-width: 95%;
        max-height: 85%;
      }
    }

    @media (max-width: 480px) {
      .title {
        font-size: clamp(1.5rem, 8vw, 2.5rem);
      }

      .bio h2 {
        font-size: 1.3rem;
      }

      .personality-trait {
        padding: 0.5rem 0.6rem;
      }

      .trait-text {
        font-size: 0.85rem;
      }

      .watt-in-action {
        margin: 2rem 0;
        padding: 1.5rem;
      }

      .watt-in-action h2 {
        font-size: 1.5rem;
      }

      .action-gallery {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
      }

      .action-card img {
        height: 150px;
      }

      .action-caption {
        padding: 0.8rem;
        font-size: 0.8rem;
        min-height: 70px;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1 class="title" data-text="WattWolf's Ref" role="banner">
      <span class="emoji-inline" aria-hidden="true">⚡</span>
      <span class="title-line">WattWolf's Ref</span>
      <span class="emoji-inline" aria-hidden="true">❤️</span>
      <div class="emoji-row" aria-hidden="true"><span>⚡</span><span>❤️</span></div>
    </h1>

    <nav class="profile-link" role="navigation" aria-label="Social media links">
      <a href="https://watt.goes-aw.ooo" target="_blank" rel="noopener noreferrer" class="bluesky-link" aria-label="Follow WattWolf on BlueSky">
        <svg class="bluesky-icon" viewBox="0 0 568 501" fill="currentColor" aria-hidden="true">
          <path d="M123.121 33.664C188.241 82.553 258.281 181.68 284 234.873c25.719-53.192 95.759-152.32 160.879-201.209C491.866-1.611 568-28.906 568 57.947c0 17.346-9.945 145.713-15.778 166.555-20.275 72.453-94.155 90.933-159.875 79.748C507.222 323.8 536.444 388.56 473.333 453.32c-119.86 122.992-172.272-30.859-185.702-70.281-2.462-7.227-3.614-10.608-3.631-7.733-.017-2.875-1.169.506-3.631 7.733-13.43 39.422-65.842 193.273-185.702 70.281-63.111-64.76-33.889-129.52 80.986-149.07-65.72 11.185-139.6-7.295-159.875-79.748C9.945 203.66 0 75.293 0 57.947 0-28.906 76.134-1.611 123.121 33.664z"/>
        </svg>
        @watt.goes-aw.ooo
      </a>
    </nav>
  </header>

  <main>
    <section class="bio" aria-labelledby="about-heading">
    <h2 id="about-heading">About Watt</h2>
    <p><strong>Name:</strong> "Watt" / WattWolf</p>
    <p><strong>Species:</strong> Wolf</p>
    <p><strong>Gender:</strong> Agender (any pronouns)</p>
    <p><strong>Sexuality:</strong> Asexual</p>
    <p><strong>Colors:</strong></p>
    <ul>
	    <li>Mountain Dew Baja Blast inspired</li> 
	    <li>Electric blue, neon green, teal, white, dark grey</li>
	    <li>Has sideways lightning-bolt shaped scar under/next to their left eye</li>
    </ul>
    <p><strong>Personality:</strong></p>
    <div class="personality-grid">
      <div class="personality-trait">
        <span class="trait-icon">😊</span>
        <span class="trait-text">friendly</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🌟</span>
        <span class="trait-text">outgoing</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🤝</span>
        <span class="trait-text">helpful</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🎲</span>
        <span class="trait-text">spontaneous</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">😄</span>
        <span class="trait-text">silly</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">👥</span>
        <span class="trait-text">social</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🤔</span>
        <span class="trait-text">a little forgetful</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">⚡</span>
        <span class="trait-text">technical/electrical nerd</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🏞️</span>
        <span class="trait-text">loves the outdoors</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🥤</span>
        <span class="trait-text">loves Baja Blast</span>
      </div>
    </div>
  </div>

  </main>

  <section class="gallery" aria-labelledby="gallery-heading">
    <h2 id="gallery-heading" class="sr-only">Art Gallery</h2>
    <div class="grid" role="grid" aria-label="WattWolf art reference gallery">
      <article class="card teal" data-href="/a/optimized/ref-teal.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/ref-teal-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/ref-teal-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/ref-teal-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/ref-teal-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/ref-teal-large.webp" type="image/webp">
          <source srcset="/a/optimized/ref-teal-large.jpg" type="image/jpeg">
          <img src="/a/ref-teal.jpeg" alt="WattWolf character reference sheet with teal background showing full body front and back views, featuring electric blue and neon green fur colors with lightning bolt scar" loading="lazy" />
        </picture>
        <p class="caption">
          Original ref sheet (Watt 2.0, 2024)<br>
          <em>(Artist: <a href="https://bsky.app/profile/theonlyrobottic.bsky.social" target="_blank" rel="noopener noreferrer">@theonlyrobottic.bsky.social</a>)</em>
        </p>
      </article>

      <article class="card updated" data-href="/a/optimized/ref-sheet-updated.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/ref-sheet-updated-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/ref-sheet-updated-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/ref-sheet-updated-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/ref-sheet-updated-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/ref-sheet-updated-large.webp" type="image/webp">
          <source srcset="/a/optimized/ref-sheet-updated-large.jpg" type="image/jpeg">
          <img src="/a/ref-sheet-updated.png" alt="Updated WattWolf character reference sheet with refined color palette showing detailed front and back views with improved Baja Blast inspired colors" loading="lazy" />
        </picture>
        <p class="caption">
          Updated ref sheet palette (Watt 2.1, 2025)<br>
          <em>(Artist: <a href="https://bsky.app/profile/theonlyrobottic.bsky.social" target="_blank" rel="noopener noreferrer">@theonlyrobottic.bsky.social</a>)</em>
        </p>
      </article>

      <article class="card baja" data-href="/a/optimized/baja.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/baja-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/baja-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/baja-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/baja-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/baja-large.webp" type="image/webp">
          <source srcset="/a/optimized/baja-large.jpg" type="image/jpeg">
          <img src="/a/baja.png" alt="Cute sticker art of WattWolf enthusiastically drinking Mountain Dew Baja Blast, showing their love for the signature drink" loading="lazy" />
        </picture>
        <p class="caption">
          Sticker: Watt chugging Baja Blast<br>
          <em>(Artist: <a href="https://www.instagram.com/korupijones" target="_blank" rel="noopener noreferrer">@korupijones on Instagram</a>)</em>
        </p>
      </article>

      <article class="card besties" data-href="/a/optimized/Besties_Comm.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/Besties_Comm-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/Besties_Comm-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/Besties_Comm-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/Besties_Comm-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/Besties_Comm-large.webp" type="image/webp">
          <source srcset="/a/optimized/Besties_Comm-large.jpg" type="image/jpeg">
          <img src="/a/Besties_Comm.png" alt="Full color commission artwork featuring WattWolf and their best friend Luca together in a friendly pose, showcasing their friendship" loading="lazy" />
        </picture>
        <p class="caption">
          Full art commission with my bestie Luca<br>
          <a href="https://bsky.app/profile/lucaswish.bsky.social" target="_blank" rel="noopener noreferrer">@lucaswish.bsky.social</a> 💙💚<br>
          <em>(Artist: <a href="https://www.instagram.com/korupijones" target="_blank" rel="noopener noreferrer">@korupijones on Instagram</a>)</em>
        </p>
      </article>
    </div>
  </section>

  <section class="watt-in-action" role="region" aria-labelledby="action-heading">
    <h2 id="action-heading">Watt IRL</h2>

    <div class="action-gallery" role="grid" aria-label="Watt fursuit photo gallery">
      <article class="action-card" data-href="/a/optimized/baja-chug-irl.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/baja-chug-irl-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/baja-chug-irl-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/baja-chug-irl-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/baja-chug-irl-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/baja-chug-irl-large.webp" type="image/webp">
          <source srcset="/a/optimized/baja-chug-irl-large.jpg" type="image/jpeg">
          <img src="/a/baja-chug-irl.png" alt="Watt in fursuit enthusiastically drinking Baja Blast, showing their love for the signature drink" loading="lazy" />
        </picture>
        <p class="action-caption">Dramatically chugging a giant plush Mountain Dew can 🥤</p>
      </article>

      <article class="action-card" data-href="/a/optimized/ball.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/ball-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/ball-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/ball-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/ball-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/ball-large.webp" type="image/webp">
          <source srcset="/a/optimized/ball-large.jpg" type="image/jpeg">
          <img src="/a/ball.png" alt="Watt in fursuit playing with a ball, demonstrating their playful and spontaneous nature" loading="lazy" />
        </picture>
        <p class="action-caption">Tennis ball balanced on head at picnic table 🏀</p>
      </article>

      <article class="action-card" data-href="/a/optimized/blacklight.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/blacklight-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/blacklight-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/blacklight-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/blacklight-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/blacklight-large.webp" type="image/webp">
          <source srcset="/a/optimized/blacklight-large.jpg" type="image/jpeg">
          <img src="/a/blacklight.png" alt="Watt in fursuit under blacklight showing off the electric blue and neon green colors glowing" loading="lazy" />
        </picture>
        <p class="action-caption">Glowing under blacklight - neon colors pop! ⚡</p>
      </article>

      <article class="action-card" data-href="/a/optimized/car-roof-sit.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/car-roof-sit-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/car-roof-sit-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/car-roof-sit-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/car-roof-sit-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/car-roof-sit-large.webp" type="image/webp">
          <source srcset="/a/optimized/car-roof-sit-large.jpg" type="image/jpeg">
          <img src="/a/car-roof-sit.png" alt="Watt in fursuit sitting on a car roof, showing their adventurous and spontaneous personality" loading="lazy" />
        </picture>
        <p class="action-caption">Sitting on car roof showing off green pawpads 🚗</p>
      </article>

      <article class="action-card" data-href="/a/optimized/car-roof-stand.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/car-roof-stand-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/car-roof-stand-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/car-roof-stand-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/car-roof-stand-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/car-roof-stand-large.webp" type="image/webp">
          <source srcset="/a/optimized/car-roof-stand-large.jpg" type="image/jpeg">
          <img src="/a/car-roof-stand.png" alt="Watt in fursuit standing confidently on a car roof, showing their bold and adventurous spirit" loading="lazy" />
        </picture>
        <p class="action-caption">Standing triumphantly on car roof, arms wide 🌟</p>
      </article>

      <article class="action-card" data-href="/a/optimized/carowinds-w-bestie.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/carowinds-w-bestie-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/carowinds-w-bestie-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/carowinds-w-bestie-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/carowinds-w-bestie-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/carowinds-w-bestie-large.webp" type="image/webp">
          <source srcset="/a/optimized/carowinds-w-bestie-large.jpg" type="image/jpeg">
          <img src="/a/carowinds-w-bestie.png" alt="Watt in fursuit at Carowinds theme park with their best friend, showing their social and fun-loving nature" loading="lazy" />
        </picture>
        <p class="action-caption">Theme park fun with bestie! 🎢</p>
      </article>

      <article class="action-card" data-href="/a/optimized/escalator-climbing.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/escalator-climbing-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/escalator-climbing-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/escalator-climbing-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/escalator-climbing-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/escalator-climbing-large.webp" type="image/webp">
          <source srcset="/a/optimized/escalator-climbing-large.jpg" type="image/jpeg">
          <img src="/a/escalator-climbing.png" alt="Watt in fursuit playfully climbing on an escalator, demonstrating their silly and spontaneous personality" loading="lazy" />
        </picture>
        <p class="action-caption">Playfully leaning over escalator rail 😄</p>
      </article>

      <article class="action-card" data-href="/a/optimized/furry-weekend-atlanta-window.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/furry-weekend-atlanta-window-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/furry-weekend-atlanta-window-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/furry-weekend-atlanta-window-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/furry-weekend-atlanta-window-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/furry-weekend-atlanta-window-large.webp" type="image/webp">
          <source srcset="/a/optimized/furry-weekend-atlanta-window-large.jpg" type="image/jpeg">
          <img src="/a/furry-weekend-atlanta-window.png" alt="Watt in fursuit at Furry Weekend Atlanta by a window, showing their social convention-going side" loading="lazy" />
        </picture>
        <p class="action-caption">Dramatic pose at convention window 🏨</p>
      </article>

      <article class="action-card" data-href="/a/optimized/georgia-aquarium.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/georgia-aquarium-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/georgia-aquarium-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/georgia-aquarium-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/georgia-aquarium-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/georgia-aquarium-large.webp" type="image/webp">
          <source srcset="/a/optimized/georgia-aquarium-large.jpg" type="image/jpeg">
          <img src="/a/georgia-aquarium.png" alt="Watt in fursuit at Georgia Aquarium, showing their love for exploring and learning about nature" loading="lazy" />
        </picture>
        <p class="action-caption">A group of fursuiters pose together in front of a 'Welcome to the World Famous' sign inside a large building. The blue-green wolf is on the far right, standing next to other costumed friends.</p>
      </article>

      <article class="action-card" data-href="/a/optimized/railing-climbing.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/railing-climbing-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/railing-climbing-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/railing-climbing-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/railing-climbing-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/railing-climbing-large.webp" type="image/webp">
          <source srcset="/a/optimized/railing-climbing-large.jpg" type="image/jpeg">
          <img src="/a/railing-climbing.png" alt="Watt in fursuit climbing on a railing, demonstrating their playful and adventurous outdoor spirit" loading="lazy" />
        </picture>
        <p class="action-caption">A person in a bright blue and green wolf fursuit is balancing playfully on a wooden railing with arms stretched out for balance. The scene is outdoors in a sunny park with parked cars in the background.</p>
      </article>

      <article class="action-card" data-href="/a/optimized/rock-climbing.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/rock-climbing-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/rock-climbing-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/rock-climbing-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/rock-climbing-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/rock-climbing-large.webp" type="image/webp">
          <source srcset="/a/optimized/rock-climbing-large.jpg" type="image/jpeg">
          <img src="/a/rock-climbing.png" alt="Watt in fursuit rock climbing, showing their love for outdoor adventures and physical challenges" loading="lazy" />
        </picture>
        <p class="action-caption">A fursuiter in bright blue and green fur poses dramatically on fake rocks indoors, next to a sign that reads 'KEEP OFF OF THE ROCKS.'</p>
      </article>

      <article class="action-card" data-href="/a/optimized/watt-and-kitty.webp" role="gridcell" tabindex="0">
        <picture>
          <source media="(max-width: 480px)" srcset="/a/optimized/watt-and-kitty-small.webp" type="image/webp">
          <source media="(max-width: 480px)" srcset="/a/optimized/watt-and-kitty-small.jpg" type="image/jpeg">
          <source media="(max-width: 768px)" srcset="/a/optimized/watt-and-kitty-medium.webp" type="image/webp">
          <source media="(max-width: 768px)" srcset="/a/optimized/watt-and-kitty-medium.jpg" type="image/jpeg">
          <source srcset="/a/optimized/watt-and-kitty-large.webp" type="image/webp">
          <source srcset="/a/optimized/watt-and-kitty-large.jpg" type="image/jpeg">
          <img src="/a/watt-and-kitty.png" alt="Watt in fursuit with a cat, showing their gentle and friendly nature with animals" loading="lazy" />
        </picture>
        <p class="action-caption">A wolf fursuit character sits inside a house while petting a curious cat that's wearing a green dinosaur-like costume piece on its back.</p>
      </article>
    </div>
  </section>

  <!-- Lightbox Modal -->
  <div id="lightbox" class="lightbox" role="dialog" aria-modal="true" aria-labelledby="lightbox-title" aria-hidden="true">
    <div class="lightbox-content">
      <button class="lightbox-close" aria-label="Close image viewer">&times;</button>
      <img id="lightbox-img" src="" alt="" />
      <div id="lightbox-title" class="sr-only"></div>
    </div>
  </div>

  <script>
    // Lightbox functionality
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightbox-img');
    const lightboxTitle = document.getElementById('lightbox-title');
    const lightboxClose = document.querySelector('.lightbox-close');

    function openLightbox(imageSrc, imageAlt) {
      lightboxImg.src = imageSrc;
      lightboxImg.alt = imageAlt;
      lightboxTitle.textContent = imageAlt;
      lightbox.classList.add('active');
      lightbox.setAttribute('aria-hidden', 'false');
      document.body.style.overflow = 'hidden';
      lightboxClose.focus();
    }

    function closeLightbox() {
      lightbox.classList.remove('active');
      lightbox.setAttribute('aria-hidden', 'true');
      document.body.style.overflow = '';
    }

    document.querySelectorAll('.card, .action-card').forEach(card => {
      // Click handler
      card.addEventListener('click', e => {
        if (!e.target.closest('a')) {
          e.preventDefault();
          const img = card.querySelector('img');
          const highResUrl = card.getAttribute('data-href');
          openLightbox(highResUrl, img.alt);
        }
      });

      // Keyboard handler
      card.addEventListener('keydown', e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          if (!e.target.closest('a')) {
            const img = card.querySelector('img');
            const highResUrl = card.getAttribute('data-href');
            openLightbox(highResUrl, img.alt);
          }
        }
      });
    });

    // Close lightbox handlers
    lightboxClose.addEventListener('click', closeLightbox);
    lightbox.addEventListener('click', e => {
      if (e.target === lightbox) {
        closeLightbox();
      }
    });

    // Keyboard navigation for lightbox
    document.addEventListener('keydown', e => {
      if (lightbox.classList.contains('active')) {
        if (e.key === 'Escape') {
          closeLightbox();
        }
      }
    });

    // Image loading optimization
    document.querySelectorAll('.card img, .action-card img').forEach(img => {
      img.classList.add('loading');

      if (img.complete) {
        img.classList.remove('loading');
        img.classList.add('loaded');
      } else {
        img.addEventListener('load', () => {
          img.classList.remove('loading');
          img.classList.add('loaded');
        });

        img.addEventListener('error', () => {
          img.classList.remove('loading');
          img.alt = 'Image failed to load';
        });
      }
    });

    // Add staggered animation to cards
    document.querySelectorAll('.card, .action-card').forEach((card, index) => {
      card.style.animationDelay = `${0.1 * index}s`;
      card.style.animation = 'slideInUp 0.6s ease-out both';
    });

    // Smooth scrolling for any internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  </script>
</body>
</html>

