<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>WattWolf's Ref 💙💚</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Character reference sheet for WattWolf - an agender wolf fursona with Baja Blast inspired colors. Features personality traits, art references, and commission examples for artists and friends." />
  <meta name="keywords" content="WattWolf, fursona, wolf, character reference, furry art, commission reference, Baja Blast colors" />

  <!-- Metadata for link previews -->
  <meta property="og:title" content="WattWolf's Ref 💙💚">
  <meta property="og:description" content="Art, reference, and personality info for Watt 💙💚">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://watt.goes-aw.ooo/ref-sheet">
  <meta property="og:image" content="https://watt.goes-aw.ooo/a/baja.png">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="1200">

  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<PERSON>Wolf's Ref 💙💚">
  <meta name="twitter:description" content="Art, reference, and personality info for Watt 💙💚">
  <meta name="twitter:image" content="https://watt.goes-aw.ooo/a/baja.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="/a/baja.png">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@600&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <style>
    :root {
      --baja-teal: #14e6c4;
      --electric-blue: #00c7ff;
      --neon-green: #0F0;
      --bg-dark: #0d1117;
      --text-main: #e9fafa;
      --text-muted: #c4e4e4;
      --card-bg: #1a1f25;
    }

    * {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 2rem 1rem;
      background: var(--bg-dark);
      color: var(--text-main);
      font-family: "Roboto", sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 100vh;
      animation: fadeIn 0.8s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes glow {
      0%, 100% { filter: brightness(1) drop-shadow(0 0 5px var(--electric-blue)); }
      50% { filter: brightness(1.1) drop-shadow(0 0 15px var(--electric-blue)); }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-5px); }
    }

  
   /* .title {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Orbitron", sans-serif;
  font-size: clamp(2rem, 5vw, 3rem);
  text-align: center;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  color: transparent;
  text-shadow:
    0 0 4px var(--electric-blue),
    0 0 8px #00d9bf,
    0 0 12px #00eb7f,
    0 0 16px var(--neon-green);
  margin: 0 0 1rem;
  line-height: 1.2;
}
*/

.title {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Orbitron", sans-serif;
  font-size: clamp(2rem, 5vw, 3rem);
  text-align: center;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin: 0 0 1rem;
  line-height: 1.2;
  animation: float 3s ease-in-out infinite;
}

.title::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  filter: blur(10px);
  z-index: -1;
  margin-top: 5px;
  animation: glow 2s ease-in-out infinite;
}



.title-line {
  display: inline;
}

.emoji-row {
  display: none;
  gap: 0.5rem;
  font-size: 1.6em;
}

/*@media (min-width: 501px) {*/
  .title {
    flex-direction: row;
    gap: 0.5rem;
  }

  .emoji-row {
    display: none;
  }

  .title .emoji-inline {
    display: inline;
    font-size: 1.2em;
  }
/*}

@media (max-width: 500px) {
  .emoji-inline {
    display: none;
  }

  .emoji-row {
    display: flex;
    margin-top: 0.3rem;
  }
}*/
    
    .profile-link {
      font-family: "Roboto", sans-serif;
      margin-bottom: 2rem;
      font-size: 1rem;
    }

    .profile-link a {
      color: var(--baja-teal);
      text-decoration: none;
    }

    .profile-link a:hover {
      text-decoration: underline;
    }

    .bio {
      max-width: 900px;
      background: #151a20;
      border: 2px solid var(--electric-blue);
      padding: 1.5rem;
      border-radius: 10px;
      margin-bottom: 2rem;
      position: relative;
      overflow: hidden;
      animation: slideInUp 0.6s ease-out 0.2s both;
    }

    .bio::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--electric-blue), var(--neon-green), var(--baja-teal));
      animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes slideInUp {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes shimmer {
      0%, 100% { transform: translateX(-100%); }
      50% { transform: translateX(100%); }
    }

    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }

    .bio h2 {
      margin-top: 0;
      font-family: "Orbitron", sans-serif;
      color: var(--electric-blue);
      font-size: 1.5rem;
    }

    .bio p {
      margin: 0.3rem 0;
    }

    .bio ul {
      padding-left: 1.2rem;
      margin: 0.5rem 0;
    }

    .personality-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.8rem;
      margin: 1rem 0;
    }

    .personality-trait {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.6rem 0.8rem;
      background: rgba(20, 230, 196, 0.1);
      border: 1px solid rgba(20, 230, 196, 0.3);
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: default;
    }

    .personality-trait:hover {
      background: rgba(20, 230, 196, 0.2);
      border-color: var(--baja-teal);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(20, 230, 196, 0.2);
    }

    .trait-icon {
      font-size: 1.2em;
      flex-shrink: 0;
    }

    .trait-text {
      font-size: 0.9rem;
      font-weight: 500;
    }

    @media (max-width: 600px) {
      .personality-grid {
        grid-template-columns: 1fr;
      }
    }

    .grid {
      display: grid;
      gap: 1.5rem;
      width: 100%;
      max-width: 1000px;
      grid-template-areas:
        "teal updated"
        "baja updated"
        "besties besties";
      grid-template-columns: 1fr 1fr;
    }

    .card {
      position: relative;
      background: var(--card-bg);
      border-radius: 10px;
      box-shadow: 0 0 0 3px rgba(20, 230, 196, 0.3);
      padding: 1rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      overflow: hidden;
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(20, 230, 196, 0.1), transparent);
      transition: left 0.5s;
      z-index: 1;
    }

    .card:hover::before {
      left: 100%;
    }

    .card:hover,
    .card:focus {
      transform: translateY(-8px) scale(1.02);
      box-shadow:
        0 0 20px var(--baja-teal),
        0 10px 30px rgba(20, 230, 196, 0.2);
      outline: 2px solid var(--electric-blue);
      outline-offset: 2px;
    }

    .card img {
      width: 100%;
      height: auto;
      border-radius: 6px;
      display: block;
      transition: opacity 0.3s ease;
    }

    .card img.loading {
      opacity: 0;
    }

    .card img.loaded {
      opacity: 1;
    }

    .image-placeholder {
      width: 100%;
      height: 200px;
      background: linear-gradient(90deg, #1a1f25 25%, #2a2f35 50%, #1a1f25 75%);
      background-size: 200% 100%;
      animation: shimmerLoad 1.5s infinite;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-muted);
      font-size: 0.9rem;
    }

    @keyframes shimmerLoad {
      0% { background-position: -200% 0; }
      100% { background-position: 200% 0; }
    }

    .caption {
      margin-top: 0.75rem;
      font-style: italic;
      font-weight: 500;
      color: var(--text-muted);
      text-align: center;
    }

    .caption a {
      color: var(--electric-blue);
      text-decoration: none;
    }

    .caption a:hover {
      text-decoration: underline;
    }

    .teal    { grid-area: teal; }
    .updated { grid-area: updated; }
    .baja    { grid-area: baja; }
    .besties { grid-area: besties; }

    /* Lightbox styles */
    .lightbox {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.9);
      animation: fadeIn 0.3s ease-out;
    }

    .lightbox.active {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .lightbox-content {
      position: relative;
      max-width: 90%;
      max-height: 90%;
      animation: zoomIn 0.3s ease-out;
    }

    .lightbox img {
      width: 100%;
      height: auto;
      border-radius: 8px;
      box-shadow: 0 0 30px rgba(20, 230, 196, 0.5);
    }

    .lightbox-close {
      position: absolute;
      top: -40px;
      right: 0;
      color: var(--text-main);
      font-size: 2rem;
      font-weight: bold;
      cursor: pointer;
      background: none;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      transition: all 0.2s;
    }

    .lightbox-close:hover,
    .lightbox-close:focus {
      color: var(--electric-blue);
      background: rgba(255, 255, 255, 0.1);
      outline: 2px solid var(--electric-blue);
    }

    @keyframes zoomIn {
      from { transform: scale(0.8); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }

    @media (max-width: 700px) {
      body {
        padding: 1rem 0.5rem;
      }

      .bio {
        padding: 1rem;
        margin-bottom: 1.5rem;
      }

      .grid {
        grid-template-areas:
          "teal"
          "updated"
          "baja"
          "besties";
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .card {
        padding: 0.8rem;
      }

      .lightbox-close {
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.7);
      }

      .lightbox-content {
        max-width: 95%;
        max-height: 85%;
      }
    }

    @media (max-width: 480px) {
      .title {
        font-size: clamp(1.5rem, 8vw, 2.5rem);
      }

      .bio h2 {
        font-size: 1.3rem;
      }

      .personality-trait {
        padding: 0.5rem 0.6rem;
      }

      .trait-text {
        font-size: 0.85rem;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1 class="title" data-text="WattWolf's Ref" role="banner">
      <span class="emoji-inline" aria-hidden="true">⚡</span>
      <span class="title-line">WattWolf's Ref</span>
      <span class="emoji-inline" aria-hidden="true">❤️</span>
      <div class="emoji-row" aria-hidden="true"><span>⚡</span><span>❤️</span></div>
    </h1>

    <nav class="profile-link" role="navigation" aria-label="Social media links">
      BlueSky: <a href="https://watt.goes-aw.ooo" target="_blank" rel="noopener noreferrer">@watt.goes-aw.ooo</a>
    </nav>
  </header>

  <main>
    <section class="bio" aria-labelledby="about-heading">
    <h2 id="about-heading">About Watt</h2>
    <p><strong>Name:</strong> "Watt" / WattWolf</p>
    <p><strong>Species:</strong> Wolf</p>
    <p><strong>Gender:</strong> Agender (any pronouns)</p>
    <p><strong>Sexuality:</strong> Asexual</p>
    <p><strong>Colors:</strong></p>
    <ul>
	    <li>Mountain Dew Baja Blast inspired</li> 
	    <li>Electric blue, neon green, teal, white, dark grey</li>
	    <li>Has sideways lightning-bolt shaped scar under/next to their left eye</li>
    </ul>
    <p><strong>Personality:</strong></p>
    <div class="personality-grid">
      <div class="personality-trait">
        <span class="trait-icon">😊</span>
        <span class="trait-text">friendly</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🌟</span>
        <span class="trait-text">outgoing</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🤝</span>
        <span class="trait-text">helpful</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🎲</span>
        <span class="trait-text">spontaneous</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">😄</span>
        <span class="trait-text">silly</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">👥</span>
        <span class="trait-text">social</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🤔</span>
        <span class="trait-text">a little forgetful</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">⚡</span>
        <span class="trait-text">technical/electrical nerd</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🏞️</span>
        <span class="trait-text">loves the outdoors</span>
      </div>
      <div class="personality-trait">
        <span class="trait-icon">🥤</span>
        <span class="trait-text">loves Baja Blast</span>
      </div>
    </div>
  </div>

  </main>

  <section class="gallery" aria-labelledby="gallery-heading">
    <h2 id="gallery-heading" class="sr-only">Art Gallery</h2>
    <div class="grid" role="grid" aria-label="WattWolf art reference gallery">
      <article class="card teal" data-href="/a/ref-teal.jpeg" role="gridcell" tabindex="0">
        <img src="/a/ref-teal.jpeg" alt="WattWolf character reference sheet with teal background showing full body front and back views, featuring electric blue and neon green fur colors with lightning bolt scar" loading="lazy" />
        <p class="caption">
          Original ref sheet (Watt 2.0, 2024)<br>
          <em>(Artist: <a href="https://bsky.app/profile/theonlyrobottic.bsky.social" target="_blank" rel="noopener noreferrer">@theonlyrobottic.bsky.social</a>)</em>
        </p>
      </article>

      <article class="card updated" data-href="/a/ref-sheet-updated.png" role="gridcell" tabindex="0">
        <img src="/a/ref-sheet-updated.png" alt="Updated WattWolf character reference sheet with refined color palette showing detailed front and back views with improved Baja Blast inspired colors" loading="lazy" />
        <p class="caption">
          Updated ref sheet palette (Watt 2.1, 2025)<br>
          <em>(Artist: <a href="https://bsky.app/profile/theonlyrobottic.bsky.social" target="_blank" rel="noopener noreferrer">@theonlyrobottic.bsky.social</a>)</em>
        </p>
      </article>

      <article class="card baja" data-href="/a/baja.png" role="gridcell" tabindex="0">
        <img src="/a/baja.png" alt="Cute sticker art of WattWolf enthusiastically drinking Mountain Dew Baja Blast, showing their love for the signature drink" loading="lazy" />
        <p class="caption">
          Sticker: Watt chugging Baja Blast<br>
          <em>(Artist: <a href="https://www.instagram.com/korupijones" target="_blank" rel="noopener noreferrer">@korupijones on Instagram</a>)</em>
        </p>
      </article>

      <article class="card besties" data-href="/a/Besties_Comm.png" role="gridcell" tabindex="0">
        <img src="/a/Besties_Comm.png" alt="Full color commission artwork featuring WattWolf and their best friend Luca together in a friendly pose, showcasing their friendship" loading="lazy" />
        <p class="caption">
          Full art commission with my bestie Luca<br>
          <a href="https://bsky.app/profile/lucaswish.bsky.social" target="_blank" rel="noopener noreferrer">@lucaswish.bsky.social</a> 💙💚<br>
          <em>(Artist: <a href="https://www.instagram.com/korupijones" target="_blank" rel="noopener noreferrer">@korupijones on Instagram</a>)</em>
        </p>
      </article>
    </div>
  </section>

  <!-- Lightbox Modal -->
  <div id="lightbox" class="lightbox" role="dialog" aria-modal="true" aria-labelledby="lightbox-title" aria-hidden="true">
    <div class="lightbox-content">
      <button class="lightbox-close" aria-label="Close image viewer">&times;</button>
      <img id="lightbox-img" src="" alt="" />
      <div id="lightbox-title" class="sr-only"></div>
    </div>
  </div>

  <script>
    // Lightbox functionality
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightbox-img');
    const lightboxTitle = document.getElementById('lightbox-title');
    const lightboxClose = document.querySelector('.lightbox-close');

    function openLightbox(imageSrc, imageAlt) {
      lightboxImg.src = imageSrc;
      lightboxImg.alt = imageAlt;
      lightboxTitle.textContent = imageAlt;
      lightbox.classList.add('active');
      lightbox.setAttribute('aria-hidden', 'false');
      document.body.style.overflow = 'hidden';
      lightboxClose.focus();
    }

    function closeLightbox() {
      lightbox.classList.remove('active');
      lightbox.setAttribute('aria-hidden', 'true');
      document.body.style.overflow = '';
    }

    document.querySelectorAll('.card').forEach(card => {
      // Click handler
      card.addEventListener('click', e => {
        if (!e.target.closest('a')) {
          e.preventDefault();
          const img = card.querySelector('img');
          openLightbox(img.src, img.alt);
        }
      });

      // Keyboard handler
      card.addEventListener('keydown', e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          if (!e.target.closest('a')) {
            const img = card.querySelector('img');
            openLightbox(img.src, img.alt);
          }
        }
      });
    });

    // Close lightbox handlers
    lightboxClose.addEventListener('click', closeLightbox);
    lightbox.addEventListener('click', e => {
      if (e.target === lightbox) {
        closeLightbox();
      }
    });

    // Keyboard navigation for lightbox
    document.addEventListener('keydown', e => {
      if (lightbox.classList.contains('active')) {
        if (e.key === 'Escape') {
          closeLightbox();
        }
      }
    });

    // Image loading optimization
    document.querySelectorAll('.card img').forEach(img => {
      img.classList.add('loading');

      if (img.complete) {
        img.classList.remove('loading');
        img.classList.add('loaded');
      } else {
        img.addEventListener('load', () => {
          img.classList.remove('loading');
          img.classList.add('loaded');
        });

        img.addEventListener('error', () => {
          img.classList.remove('loading');
          img.alt = 'Image failed to load';
        });
      }
    });

    // Add staggered animation to cards
    document.querySelectorAll('.card').forEach((card, index) => {
      card.style.animationDelay = `${0.1 * index}s`;
      card.style.animation = 'slideInUp 0.6s ease-out both';
    });

    // Smooth scrolling for any internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  </script>
</body>
</html>

